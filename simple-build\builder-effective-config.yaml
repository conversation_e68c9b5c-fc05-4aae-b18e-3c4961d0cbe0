directories:
  output: simple-build
  buildResources: assets
appId: com.chunginlee.interviewcoder
productName: Interview Coder
files:
  - filter:
      - dist/**/*
      - dist-electron/**/*
      - package.json
      - electron/**/*
asar: true
compression: maximum
generateUpdatesFilesForAllChannels: true
mac:
  category: public.app-category.developer-tools
  target:
    - target: dmg
      arch:
        - x64
        - arm64
    - target: zip
      arch:
        - x64
        - arm64
  artifactName: Interview-Coder-${arch}.${ext}
  icon: assets/icons/mac/icon.icns
  hardenedRuntime: true
  gatekeeperAssess: false
  entitlements: build/entitlements.mac.plist
  entitlementsInherit: build/entitlements.mac.plist
  identity: Developer ID Application
  notarize: true
  protocols:
    name: interview-coder-protocol
    schemes:
      - interview-coder
win:
  target: portable
  icon: assets/icons/win/icon.ico
  artifactName: ${productName}-Windows-${version}-${arch}.${ext}
  requestedExecutionLevel: asInvoker
  forceCodeSigning: false
  protocols:
    name: interview-coder-protocol
    schemes:
      - interview-coder
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Interview Coder
linux:
  target:
    - AppImage
  icon: assets/icons/png/icon-256x256.png
  artifactName: ${productName}-Linux-${version}.${ext}
  protocols:
    name: interview-coder-protocol
    schemes:
      - interview-coder
publish:
  - provider: github
    owner: ibttf
    repo: interview-coder
    private: false
    releaseType: release
extraMetadata:
  main: dist-electron/main.js
electronVersion: 29.4.6
