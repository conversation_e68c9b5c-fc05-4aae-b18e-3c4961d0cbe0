# Interview Coder - Automated Build Script
Write-Host "🚀 Interview Coder - Automated Build Script" -ForegroundColor Cyan
Write-Host "=" * 50 -ForegroundColor Cyan

# Function to kill processes safely
function Kill-ProcessesSafely {
    Write-Host "🔄 Killing any running electron/node processes..." -ForegroundColor Yellow
    
    try {
        Get-Process | Where-Object {$_.ProcessName -like "*electron*" -or $_.ProcessName -like "*node*" -or $_.ProcessName -like "*Interview*"} | Stop-Process -Force -ErrorAction SilentlyContinue
        Write-Host "✓ Processes killed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "⚠️ Some processes couldn't be killed (this is usually fine)" -ForegroundColor Yellow
    }
    
    Start-Sleep -Seconds 3
}

# Function to clean directories
function Clean-BuildDirectories {
    Write-Host "🧹 Cleaning build directories..." -ForegroundColor Yellow
    
    $directories = @("release", "build-output", "dist", "dist-electron")
    
    foreach ($dir in $directories) {
        if (Test-Path $dir) {
            try {
                Remove-Item -Path $dir -Recurse -Force -ErrorAction Stop
                Write-Host "✓ Removed $dir" -ForegroundColor Green
            }
            catch {
                Write-Host "⚠️ Couldn't remove $dir (might be in use)" -ForegroundColor Yellow
                # Try alternative method
                try {
                    robocopy "C:\Windows\Temp" $dir /MIR /R:0 /W:0 2>$null
                    Remove-Item -Path $dir -Recurse -Force -ErrorAction Stop
                    Write-Host "✓ Force removed $dir using robocopy" -ForegroundColor Green
                }
                catch {
                    Write-Host "❌ Failed to remove $dir - will try building to different location" -ForegroundColor Red
                }
            }
        }
    }
}

# Function to build application
function Build-Application {
    Write-Host "🔨 Building the application..." -ForegroundColor Yellow
    
    try {
        npm run build
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✓ Application built successfully" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "❌ Application build failed" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Application build failed with exception: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to package for Windows
function Package-ForWindows {
    Write-Host "📦 Packaging for Windows..." -ForegroundColor Yellow
    
    # Try multiple packaging strategies
    $strategies = @(
        @{
            name = "Standard build to build-output"
            command = "npx electron-builder build --win --publish=never --config.directories.output=build-output"
        },
        @{
            name = "Portable build without ASAR"
            command = "npx electron-builder build --win --config.win.target=portable --publish=never --config.directories.output=build-output --config.asar=false"
        },
        @{
            name = "Simple portable build"
            command = "npx electron-builder build --win --config.win.target=portable --publish=never --config.directories.output=simple-build"
        }
    )
    
    foreach ($strategy in $strategies) {
        Write-Host "🔄 Trying: $($strategy.name)" -ForegroundColor Cyan
        
        try {
            Invoke-Expression $strategy.command
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ SUCCESS: $($strategy.name) completed!" -ForegroundColor Green
                return $true
            }
            else {
                Write-Host "❌ FAILED: $($strategy.name)" -ForegroundColor Red
            }
        }
        catch {
            Write-Host "❌ EXCEPTION in $($strategy.name): $($_.Exception.Message)" -ForegroundColor Red
        }
        
        Write-Host "⏳ Waiting 5 seconds before trying next strategy..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
    }
    
    return $false
}

# Function to show results
function Show-Results {
    Write-Host "`n🎯 Build Results:" -ForegroundColor Cyan
    
    $outputDirs = @("build-output", "simple-build", "release")
    $found = $false
    
    foreach ($dir in $outputDirs) {
        if (Test-Path $dir) {
            Write-Host "📁 Found output in: $dir" -ForegroundColor Green
            $files = Get-ChildItem -Path $dir -Recurse -Filter "*.exe" | Select-Object Name, Length, FullName
            
            if ($files) {
                Write-Host "🎉 Executable files created:" -ForegroundColor Green
                foreach ($file in $files) {
                    $sizeMB = [math]::Round($file.Length / 1MB, 2)
                    Write-Host "  ✓ $($file.Name) ($sizeMB MB)" -ForegroundColor White
                    Write-Host "    📍 $($file.FullName)" -ForegroundColor Gray
                }
                $found = $true
            }
        }
    }
    
    if (-not $found) {
        Write-Host "❌ No executable files found. Build may have failed." -ForegroundColor Red
        Write-Host "💡 Try restarting your computer and running this script again." -ForegroundColor Yellow
    }
}

# Main execution
try {
    Write-Host "🏁 Starting automated build process..." -ForegroundColor Green
    
    # Step 1: Kill processes
    Kill-ProcessesSafely
    
    # Step 2: Clean directories
    Clean-BuildDirectories
    
    # Step 3: Build application
    if (-not (Build-Application)) {
        Write-Host "❌ Build failed. Cannot proceed with packaging." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    
    # Step 4: Package for Windows
    if (Package-ForWindows) {
        Write-Host "`n🎉 BUILD COMPLETED SUCCESSFULLY!" -ForegroundColor Green
        Show-Results
    }
    else {
        Write-Host "`n❌ All packaging strategies failed." -ForegroundColor Red
        Write-Host "💡 Recommendations:" -ForegroundColor Yellow
        Write-Host "  1. Restart your computer" -ForegroundColor White
        Write-Host "  2. Run PowerShell as Administrator" -ForegroundColor White
        Write-Host "  3. Temporarily disable antivirus" -ForegroundColor White
        Write-Host "  4. Try running this script again" -ForegroundColor White
    }
}
catch {
    Write-Host "❌ Script failed with error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 Script completed. Check the output above for results." -ForegroundColor Cyan
Read-Host "Press Enter to exit"
