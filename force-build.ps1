# Interview Coder - Force Build Script (Handles File Locking)
Write-Host "Interview Coder - Force Build Script" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan

# Function to force kill processes and unlock files
function Force-UnlockFiles {
    Write-Host "Step 1: Force killing ALL related processes..." -ForegroundColor Yellow
    
    # Kill all electron, node, and related processes
    $processNames = @("electron", "node", "Interview Coder", "app-builder", "7za")
    
    foreach ($processName in $processNames) {
        try {
            Get-Process -Name $processName -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
            Write-Host "Killed $processName processes" -ForegroundColor Green
        }
        catch {
            # Ignore errors
        }
    }
    
    # Wait longer for processes to fully terminate
    Write-Host "Waiting 10 seconds for processes to fully terminate..." -ForegroundColor Yellow
    Start-Sleep -Seconds 10
    
    # Force unlock using handle.exe if available, otherwise use robocopy
    Write-Host "Step 2: Force unlocking directories..." -ForegroundColor Yellow
    
    $dirsToClean = @("simple-build", "build-output", "release", "dist", "dist-electron")
    
    foreach ($dir in $dirsToClean) {
        if (Test-Path $dir) {
            Write-Host "Attempting to unlock and remove: $dir" -ForegroundColor Cyan
            
            # Method 1: Try normal removal
            try {
                Remove-Item -Path $dir -Recurse -Force -ErrorAction Stop
                Write-Host "Successfully removed $dir (normal method)" -ForegroundColor Green
                continue
            }
            catch {
                Write-Host "Normal removal failed for $dir, trying force methods..." -ForegroundColor Yellow
            }
            
            # Method 2: Use robocopy to clear the directory
            try {
                $tempDir = Join-Path $env:TEMP "empty_$(Get-Random)"
                New-Item -ItemType Directory -Path $tempDir -Force | Out-Null
                
                # Use robocopy to mirror an empty directory (effectively deleting everything)
                robocopy $tempDir $dir /MIR /R:0 /W:0 /NFL /NDL /NJH /NJS | Out-Null
                
                # Now try to remove the directory
                Remove-Item -Path $dir -Recurse -Force -ErrorAction SilentlyContinue
                Remove-Item -Path $tempDir -Recurse -Force -ErrorAction SilentlyContinue
                
                Write-Host "Successfully removed $dir (robocopy method)" -ForegroundColor Green
            }
            catch {
                Write-Host "Could not remove $dir - will try building to different location" -ForegroundColor Red
            }
        }
    }
}

# Function to build with unique directory names
function Build-WithUniqueDir {
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $uniqueDir = "build_$timestamp"
    
    Write-Host "Step 3: Building application..." -ForegroundColor Yellow
    try {
        npm run build
        if ($LASTEXITCODE -ne 0) {
            throw "npm run build failed"
        }
        Write-Host "Application built successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "Application build failed" -ForegroundColor Red
        return $false
    }
    
    Write-Host "Step 4: Packaging to unique directory: $uniqueDir" -ForegroundColor Yellow
    
    # Try building without ASAR to avoid the app.asar locking issue
    $buildCommands = @(
        "npx electron-builder build --win --config.win.target=portable --publish=never --config.directories.output=$uniqueDir --config.asar=false",
        "npx electron-builder build --win --config.win.target=nsis --publish=never --config.directories.output=$uniqueDir --config.asar=false",
        "npx electron-builder build --win --publish=never --config.directories.output=$uniqueDir --config.asar=false"
    )
    
    foreach ($cmd in $buildCommands) {
        Write-Host "Trying: $cmd" -ForegroundColor Cyan
        
        try {
            Invoke-Expression $cmd
            if ($LASTEXITCODE -eq 0) {
                Write-Host "SUCCESS! Build completed in directory: $uniqueDir" -ForegroundColor Green
                
                # Show results
                if (Test-Path $uniqueDir) {
                    $exeFiles = Get-ChildItem -Path $uniqueDir -Recurse -Filter "*.exe"
                    if ($exeFiles) {
                        Write-Host ""
                        Write-Host "EXECUTABLE FILES CREATED:" -ForegroundColor Green
                        foreach ($file in $exeFiles) {
                            $sizeMB = [math]::Round($file.Length / 1MB, 2)
                            Write-Host "  $($file.Name) ($sizeMB MB)" -ForegroundColor White
                            Write-Host "  Location: $($file.FullName)" -ForegroundColor Gray
                        }
                        return $true
                    }
                }
            }
        }
        catch {
            Write-Host "Command failed: $cmd" -ForegroundColor Red
        }
        
        Write-Host "Waiting 5 seconds before next attempt..." -ForegroundColor Yellow
        Start-Sleep -Seconds 5
    }
    
    return $false
}

# Main execution
Write-Host "Starting force build process..." -ForegroundColor Green

# Step 1 & 2: Force unlock files
Force-UnlockFiles

# Step 3 & 4: Build with unique directory
$success = Build-WithUniqueDir

if ($success) {
    Write-Host ""
    Write-Host "================================================" -ForegroundColor Green
    Write-Host "BUILD COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "================================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Your Windows executable is ready!" -ForegroundColor Green
    Write-Host "Features included:" -ForegroundColor Cyan
    Write-Host "  - Alt+Enter for MCQ mode (fast Qwen processing)" -ForegroundColor White
    Write-Host "  - Ctrl+R for coding questions (QwQ reasoning)" -ForegroundColor White
    Write-Host "  - Gemini text extraction for all scenarios" -ForegroundColor White
    Write-Host "  - All keyboard shortcuts and functionality" -ForegroundColor White
}
else {
    Write-Host ""
    Write-Host "================================================" -ForegroundColor Red
    Write-Host "BUILD FAILED" -ForegroundColor Red
    Write-Host "================================================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Final recommendation: RESTART YOUR COMPUTER" -ForegroundColor Yellow
    Write-Host "Then run this script again as Administrator" -ForegroundColor Yellow
}

Write-Host ""
Read-Host "Press Enter to exit"
