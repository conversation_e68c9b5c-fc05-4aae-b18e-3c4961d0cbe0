@echo off
echo ========================================
echo Interview Coder - Windows Build Script
echo ========================================
echo.

echo [1/5] Checking Node.js and npm...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not available
    pause
    exit /b 1
)

echo ✓ Node.js and npm are available
echo.

echo [2/5] Installing dependencies...
call npm install
if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    pause
    exit /b 1
)
echo ✓ Dependencies installed successfully
echo.

echo [3/5] Cleaning previous builds...
call npm run clean
echo ✓ Previous builds cleaned
echo.

echo [4/5] Building the application...
call npm run build
if %errorlevel% neq 0 (
    echo ERROR: Build failed
    pause
    exit /b 1
)
echo ✓ Application built successfully
echo.

echo [5/5] Packaging for Windows...
call npx electron-builder build --win
if %errorlevel% neq 0 (
    echo ERROR: Packaging failed
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✓ BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Output files are located in the 'release' directory:
echo - Installer: Interview Coder-Windows-[version]-x64.exe
echo - Portable: Interview Coder-Windows-[version]-x64.exe (portable)
echo.
echo Features included in the build:
echo ✓ Alt+Enter for MCQ mode (fast Qwen processing)
echo ✓ Ctrl+R for coding questions (QwQ reasoning)
echo ✓ Gemini text extraction for all scenarios
echo ✓ All keyboard shortcuts and functionality
echo.
pause
