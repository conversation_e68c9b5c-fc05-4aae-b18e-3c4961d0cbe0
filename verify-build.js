#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Interview Coder - Build Verification Script');
console.log('='.repeat(50));

// Check if build files exist
const releaseDir = path.join(__dirname, 'release');
const distElectronDir = path.join(__dirname, 'dist-electron');
const distDir = path.join(__dirname, 'dist');

console.log('\n📁 Checking build directories...');

if (fs.existsSync(distElectronDir)) {
    console.log('✓ dist-electron directory exists');
    const electronFiles = fs.readdirSync(distElectronDir);
    console.log(`  - Contains ${electronFiles.length} files`);
    
    // Check for main files
    const requiredFiles = ['main.js', 'preload.js', 'ConfigHelper.js', 'ProcessingHelper.js'];
    requiredFiles.forEach(file => {
        if (electronFiles.includes(file)) {
            console.log(`  ✓ ${file} found`);
        } else {
            console.log(`  ❌ ${file} missing`);
        }
    });
} else {
    console.log('❌ dist-electron directory missing');
}

if (fs.existsSync(distDir)) {
    console.log('✓ dist directory exists');
} else {
    console.log('❌ dist directory missing');
}

if (fs.existsSync(releaseDir)) {
    console.log('✓ release directory exists');
    const releaseFiles = fs.readdirSync(releaseDir);
    console.log(`  - Contains ${releaseFiles.length} files/folders`);
    
    // Look for Windows executables
    const exeFiles = releaseFiles.filter(file => file.endsWith('.exe'));
    if (exeFiles.length > 0) {
        console.log('✓ Windows executable(s) found:');
        exeFiles.forEach(file => {
            const filePath = path.join(releaseDir, file);
            const stats = fs.statSync(filePath);
            const sizeMB = (stats.size / (1024 * 1024)).toFixed(2);
            console.log(`  - ${file} (${sizeMB} MB)`);
        });
    } else {
        console.log('❌ No Windows executables found');
    }
} else {
    console.log('❌ release directory missing');
}

// Check package.json configuration
console.log('\n⚙️  Checking package.json configuration...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

if (packageJson.build && packageJson.build.win) {
    console.log('✓ Windows build configuration found');
    const winConfig = packageJson.build.win;
    
    if (winConfig.icon) {
        const iconPath = path.join(__dirname, winConfig.icon);
        if (fs.existsSync(iconPath)) {
            console.log(`✓ Icon file exists: ${winConfig.icon}`);
        } else {
            console.log(`❌ Icon file missing: ${winConfig.icon}`);
        }
    }
    
    if (winConfig.target) {
        console.log('✓ Build targets configured:');
        winConfig.target.forEach(target => {
            if (typeof target === 'string') {
                console.log(`  - ${target}`);
            } else {
                console.log(`  - ${target.target} (${target.arch ? target.arch.join(', ') : 'default arch'})`);
            }
        });
    }
} else {
    console.log('❌ Windows build configuration missing');
}

// Check for our custom features
console.log('\n🚀 Checking implemented features...');

// Check shortcuts.ts for Alt+Enter
const shortcutsPath = path.join(__dirname, 'electron', 'shortcuts.ts');
if (fs.existsSync(shortcutsPath)) {
    const shortcutsContent = fs.readFileSync(shortcutsPath, 'utf8');
    if (shortcutsContent.includes('Alt+Return')) {
        console.log('✓ Alt+Enter shortcut for MCQ mode implemented');
    } else {
        console.log('❌ Alt+Enter shortcut not found');
    }
} else {
    console.log('❌ shortcuts.ts file not found');
}

// Check ProcessingHelper.ts for fast Qwen model
const processingHelperPath = path.join(__dirname, 'electron', 'ProcessingHelper.ts');
if (fs.existsSync(processingHelperPath)) {
    const processingContent = fs.readFileSync(processingHelperPath, 'utf8');
    if (processingContent.includes('qwen-2.5-72b-instruct:free')) {
        console.log('✓ Fast Qwen model for MCQ processing implemented');
    } else {
        console.log('❌ Fast Qwen model configuration not found');
    }
    
    if (processingContent.includes('gemini-2.0-flash')) {
        console.log('✓ Gemini text extraction implemented');
    } else {
        console.log('❌ Gemini text extraction not found');
    }
} else {
    console.log('❌ ProcessingHelper.ts file not found');
}

console.log('\n' + '='.repeat(50));
console.log('🎯 Build verification complete!');
console.log('\nIf all checks passed, your executable should include:');
console.log('• Alt+Enter for fast MCQ processing');
console.log('• Ctrl+R for coding questions');
console.log('• Gemini-powered text extraction');
console.log('• All keyboard shortcuts and features');
