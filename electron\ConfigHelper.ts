// ConfigHelper.ts
import fs from "node:fs"
import path from "node:path"
import { app } from "electron"
import { EventEmitter } from "events"
import { OpenAI } from "openai"

interface Config {
  apiKey: string; // Current provider's API key (for backward compatibility)
  apiProvider: "openai" | "gemini" | "anthropic" | "openrouter";  // Added OpenRouter provider
  extractionModel: string;
  solutionModel: string;
  debuggingModel: string;
  language: string;
  opacity: number;
  openrouterModel?: string; // For storing the OpenRouter model selection
  geminiApiKey?: string; // For storing Gemini API key when using OpenRouter
  mcqSupport?: boolean; // Flag to enable MCQ support

  // Store API keys for each provider separately
  openaiApiKey?: string;
  anthropicApiKey?: string;
  openrouterApiKey?: string;
}

export class ConfigHelper extends EventEmitter {
  private configPath: string;
  private defaultConfig: Config = {
    apiKey: "",
    apiProvider: "gemini", // Default to Gemini
    extractionModel: "gemini-2.0-flash", // Default to Flash for faster responses
    solutionModel: "gemini-2.0-flash",
    debuggingModel: "gemini-2.0-flash",
    language: "python",
    opacity: 1.0,
    openrouterModel: "qwen/qwq-32b:free", // Default OpenRouter model
    geminiApiKey: "", // For storing Gemini API key when using OpenRouter
    mcqSupport: true, // Enable MCQ support by default

    // Default empty API keys for each provider
    openaiApiKey: "",
    anthropicApiKey: "",
    openrouterApiKey: ""
  };

  constructor() {
    super();
    // Use the app's user data directory to store the config
    try {
      this.configPath = path.join(app.getPath('userData'), 'config.json');
      console.log('Config path:', this.configPath);
    } catch (err) {
      console.warn('Could not access user data path, using fallback');
      this.configPath = path.join(process.cwd(), 'config.json');
    }

    // Ensure the initial config file exists
    this.ensureConfigExists();
  }

  /**
   * Ensure config file exists
   */
  private ensureConfigExists(): void {
    try {
      if (!fs.existsSync(this.configPath)) {
        this.saveConfig(this.defaultConfig);
      }
    } catch (err) {
      console.error("Error ensuring config exists:", err);
    }
  }

  /**
   * Validate and sanitize model selection to ensure only allowed models are used
   */
  private sanitizeModelSelection(model: string, provider: "openai" | "gemini" | "anthropic" | "openrouter"): string {
    if (provider === "openai") {
      // Only allow gpt-4o and gpt-4o-mini for OpenAI
      const allowedModels = ['gpt-4o', 'gpt-4o-mini'];
      if (!allowedModels.includes(model)) {
        console.warn(`Invalid OpenAI model specified: ${model}. Using default model: gpt-4o`);
        return 'gpt-4o';
      }
      return model;
    } else if (provider === "gemini")  {
      // Only allow gemini-1.5-pro, gemini-2.0-flash, gemini-2.5-flash-preview-05-20, and gemini-2.5-flash-preview-04-17 for Gemini
      const allowedModels = ['gemini-1.5-pro', 'gemini-2.0-flash', 'gemini-2.5-flash-preview-05-20', 'gemini-2.5-flash-preview-04-17'];
      if (!allowedModels.includes(model)) {
        console.warn(`Invalid Gemini model specified: ${model}. Using default model: gemini-2.0-flash`);
        return 'gemini-2.0-flash'; // Changed default to flash
      }
      return model;
    } else if (provider === "anthropic") {
      // Only allow Claude models
      const allowedModels = ['claude-3-7-sonnet-20250219', 'claude-3-5-sonnet-20241022', 'claude-3-opus-20240229'];
      if (!allowedModels.includes(model)) {
        console.warn(`Invalid Anthropic model specified: ${model}. Using default model: claude-3-7-sonnet-20250219`);
        return 'claude-3-7-sonnet-20250219';
      }
      return model;
    } else if (provider === "openrouter") {
      // For OpenRouter, validate against known free models but allow others
      const knownFreeModels = [
        'qwen/qwq-32b:free',
        'qwen/qwen-2.5-72b-instruct:free',
        'meta-llama/llama-3.1-8b-instruct:free',
        'mistralai/devstral-small:free'
      ];

      if (!model || model.trim() === '') {
        console.warn(`No OpenRouter model specified. Using default model: qwen/qwq-32b:free`);
        return 'qwen/qwq-32b:free';
      }

      // Accept any model but warn if it's not a known free model
      if (!knownFreeModels.includes(model)) {
        console.warn(`Using custom OpenRouter model: ${model}. Note: This may incur costs if not a free model.`);
      }

      return model;
    }
    // Default fallback
    return model;
  }

  public loadConfig(): Config {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf8');
        const config = JSON.parse(configData);

        // Ensure apiProvider is a valid value
        if (config.apiProvider !== "openai" && config.apiProvider !== "gemini" && config.apiProvider !== "anthropic" && config.apiProvider !== "openrouter") {
          config.apiProvider = "gemini"; // Default to Gemini if invalid
        }

        // Sanitize model selections to ensure only allowed models are used
        if (config.extractionModel) {
          config.extractionModel = this.sanitizeModelSelection(config.extractionModel, config.apiProvider);
        }
        if (config.solutionModel) {
          config.solutionModel = this.sanitizeModelSelection(config.solutionModel, config.apiProvider);
        }
        if (config.debuggingModel) {
          config.debuggingModel = this.sanitizeModelSelection(config.debuggingModel, config.apiProvider);
        }

        // Ensure the current apiKey reflects the provider-specific key
        const mergedConfig = {
          ...this.defaultConfig,
          ...config
        };

        // Set the current apiKey based on the provider
        if (mergedConfig.apiProvider === "openai" && mergedConfig.openaiApiKey) {
          mergedConfig.apiKey = mergedConfig.openaiApiKey;
        } else if (mergedConfig.apiProvider === "gemini" && mergedConfig.geminiApiKey) {
          mergedConfig.apiKey = mergedConfig.geminiApiKey;
        } else if (mergedConfig.apiProvider === "anthropic" && mergedConfig.anthropicApiKey) {
          mergedConfig.apiKey = mergedConfig.anthropicApiKey;
        } else if (mergedConfig.apiProvider === "openrouter" && mergedConfig.openrouterApiKey) {
          mergedConfig.apiKey = mergedConfig.openrouterApiKey;
        }

        return mergedConfig;
      }

      // If no config exists, create a default one
      this.saveConfig(this.defaultConfig);
      return this.defaultConfig;
    } catch (err) {
      console.error("Error loading config:", err);
      return this.defaultConfig;
    }
  }

  /**
   * Save configuration to disk
   */
  public saveConfig(config: Config): void {
    try {
      // Ensure the directory exists
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      // Write the config file
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (err) {
      console.error("Error saving config:", err);
    }
  }

  /**
   * Update specific configuration values
   */
  public updateConfig(updates: Partial<Config>): Config {
    try {
      const currentConfig = this.loadConfig();
      let provider = updates.apiProvider || currentConfig.apiProvider;

      // Auto-detect provider based on API key format if a new key is provided
      if (updates.apiKey && !updates.apiProvider) {
        // If API key starts with "sk-", it's likely an OpenAI key
        if (updates.apiKey.trim().startsWith('sk-')) {
          provider = "openai";
          console.log("Auto-detected OpenAI API key format");
        } else if (updates.apiKey.trim().startsWith('sk-ant-')) {
          provider = "anthropic";
          console.log("Auto-detected Anthropic API key format");
        } else if (updates.apiKey.trim().startsWith('sk-or-')) {
          provider = "openrouter";
          console.log("Auto-detected OpenRouter API key format");
        } else {
          provider = "gemini";
          console.log("Using Gemini API key format (default)");
        }

        // Update the provider in the updates object
        updates.apiProvider = provider;
      }

      // Store the API key in the appropriate provider-specific field
      if (updates.apiKey) {
        if (provider === "openai") {
          updates.openaiApiKey = updates.apiKey;
        } else if (provider === "gemini") {
          updates.geminiApiKey = updates.apiKey;
        } else if (provider === "anthropic") {
          updates.anthropicApiKey = updates.apiKey;
        } else if (provider === "openrouter") {
          updates.openrouterApiKey = updates.apiKey;
        }
      }

      // If provider is changing, reset models to the default for that provider and load the appropriate API key
      if (updates.apiProvider && updates.apiProvider !== currentConfig.apiProvider) {
        if (updates.apiProvider === "openai") {
          updates.extractionModel = "gpt-4o";
          updates.solutionModel = "gpt-4o";
          updates.debuggingModel = "gpt-4o";
          // Load the stored OpenAI API key if available
          if (currentConfig.openaiApiKey && !updates.apiKey) {
            updates.apiKey = currentConfig.openaiApiKey;
          }
        } else if (updates.apiProvider === "anthropic") {
          updates.extractionModel = "claude-3-7-sonnet-20250219";
          updates.solutionModel = "claude-3-7-sonnet-20250219";
          updates.debuggingModel = "claude-3-7-sonnet-20250219";
          // Load the stored Anthropic API key if available
          if (currentConfig.anthropicApiKey && !updates.apiKey) {
            updates.apiKey = currentConfig.anthropicApiKey;
          }
        } else if (updates.apiProvider === "openrouter") {
          // For OpenRouter, we'll use Gemini for extraction and debugging (image support)
          // but use Qwen for solution generation (text-only)
          // Load the stored OpenRouter API key if available
          if (currentConfig.openrouterApiKey && !updates.apiKey) {
            updates.apiKey = currentConfig.openrouterApiKey;
          }
          updates.extractionModel = "gemini-2.0-flash"; // Always use Gemini for text extraction
          updates.solutionModel = "qwen/qwq-32b:free"; // Use Qwen for coding solutions
          updates.debuggingModel = "gemini-2.0-flash"; // Always use Gemini for debugging
          updates.openrouterModel = "qwen/qwq-32b:free";
        } else if (updates.apiProvider === "gemini") {
          updates.extractionModel = "gemini-2.0-flash";
          updates.solutionModel = "gemini-2.0-flash";
          updates.debuggingModel = "gemini-2.0-flash";
          // Load the stored Gemini API key if available
          if (currentConfig.geminiApiKey && !updates.apiKey) {
            updates.apiKey = currentConfig.geminiApiKey;
          }
        } else {
          updates.extractionModel = "gemini-2.0-flash";
          updates.solutionModel = "gemini-2.0-flash";
          updates.debuggingModel = "gemini-2.0-flash";
        }
      }

      // Sanitize model selections in the updates
      if (updates.extractionModel) {
        updates.extractionModel = this.sanitizeModelSelection(updates.extractionModel, provider);
      }
      if (updates.solutionModel) {
        updates.solutionModel = this.sanitizeModelSelection(updates.solutionModel, provider);
      }
      if (updates.debuggingModel) {
        updates.debuggingModel = this.sanitizeModelSelection(updates.debuggingModel, provider);
      }

      const newConfig = { ...currentConfig, ...updates };
      this.saveConfig(newConfig);

      // Only emit update event for changes other than opacity
      // This prevents re-initializing the AI client when only opacity changes
      if (updates.apiKey !== undefined || updates.apiProvider !== undefined ||
          updates.extractionModel !== undefined || updates.solutionModel !== undefined ||
          updates.debuggingModel !== undefined || updates.language !== undefined) {
        this.emit('config-updated', newConfig);
      }

      return newConfig;
    } catch (error) {
      console.error('Error updating config:', error);
      return this.defaultConfig;
    }
  }

  /**
   * Check if the API key is configured
   */
  public hasApiKey(): boolean {
    const config = this.loadConfig();
    return !!config.apiKey && config.apiKey.trim().length > 0;
  }

  /**
   * Validate the API key format
   */
  public isValidApiKeyFormat(apiKey: string, provider?: "openai" | "gemini" | "anthropic" | "openrouter" ): boolean {
    // If provider is not specified, attempt to auto-detect
    if (!provider) {
      if (apiKey.trim().startsWith('sk-')) {
        if (apiKey.trim().startsWith('sk-ant-')) {
          provider = "anthropic";
        } else if (apiKey.trim().startsWith('sk-or-')) {
          provider = "openrouter";
        } else {
          provider = "openai";
        }
      } else {
        provider = "gemini";
      }
    }

    if (provider === "openai") {
      // Basic format validation for OpenAI API keys
      return /^sk-[a-zA-Z0-9]{32,}$/.test(apiKey.trim());
    } else if (provider === "gemini") {
      // Basic format validation for Gemini API keys (usually alphanumeric with no specific prefix)
      return apiKey.trim().length >= 10; // Assuming Gemini keys are at least 10 chars
    } else if (provider === "anthropic") {
      // Basic format validation for Anthropic API keys
      return /^sk-ant-[a-zA-Z0-9]{32,}$/.test(apiKey.trim());
    } else if (provider === "openrouter") {
      // Basic format validation for OpenRouter API keys
      return /^sk-or-[a-zA-Z0-9]{32,}$/.test(apiKey.trim());
    }

    return false;
  }

  /**
   * Get the stored opacity value
   */
  public getOpacity(): number {
    const config = this.loadConfig();
    return config.opacity !== undefined ? config.opacity : 1.0;
  }

  /**
   * Set the window opacity value
   */
  public setOpacity(opacity: number): void {
    // Ensure opacity is between 0.1 and 1.0
    const validOpacity = Math.min(1.0, Math.max(0.1, opacity));
    this.updateConfig({ opacity: validOpacity });
  }

  /**
   * Get the preferred programming language
   */
  public getLanguage(): string {
    const config = this.loadConfig();
    return config.language || "python";
  }

  /**
   * Set the preferred programming language
   */
  public setLanguage(language: string): void {
    this.updateConfig({ language });
  }

  /**
   * Test API key with the selected provider
   */
  public async testApiKey(apiKey: string, provider?: "openai" | "gemini" | "anthropic" | "openrouter"): Promise<{valid: boolean, error?: string}> {
    // Auto-detect provider based on key format if not specified
    if (!provider) {
      if (apiKey.trim().startsWith('sk-')) {
        if (apiKey.trim().startsWith('sk-ant-')) {
          provider = "anthropic";
          console.log("Auto-detected Anthropic API key format for testing");
        } else if (apiKey.trim().startsWith('sk-or-')) {
          provider = "openrouter";
          console.log("Auto-detected OpenRouter API key format for testing");
        } else {
          provider = "openai";
          console.log("Auto-detected OpenAI API key format for testing");
        }
      } else {
        provider = "gemini";
        console.log("Using Gemini API key format for testing (default)");
      }
    }

    if (provider === "openai") {
      return this.testOpenAIKey(apiKey);
    } else if (provider === "gemini") {
      return this.testGeminiKey(apiKey);
    } else if (provider === "anthropic") {
      return this.testAnthropicKey(apiKey);
    } else if (provider === "openrouter") {
      return this.testOpenRouterKey(apiKey);
    }

    return { valid: false, error: "Unknown API provider" };
  }

  /**
   * Test OpenAI API key
   */
  private async testOpenAIKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      const openai = new OpenAI({ apiKey });
      // Make a simple API call to test the key
      await openai.models.list();
      return { valid: true };
    } catch (error: any) {
      console.error('OpenAI API key test failed:', error);

      // Determine the specific error type for better error messages
      let errorMessage = 'Unknown error validating OpenAI API key';

      if (error.status === 401) {
        errorMessage = 'Invalid API key. Please check your OpenAI key and try again.';
      } else if (error.status === 429) {
        errorMessage = 'Rate limit exceeded. Your OpenAI API key has reached its request limit or has insufficient quota.';
      } else if (error.status === 500) {
        errorMessage = 'OpenAI server error. Please try again later.';
      } else if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }

      return { valid: false, error: errorMessage };
    }
  }

  /**
   * Test Gemini API key
   * Note: This is a simplified implementation since we don't have the actual Gemini client
   */
  private async testGeminiKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      // For now, we'll just do a basic check to ensure the key exists and has valid format
      // In production, you would connect to the Gemini API and validate the key
      if (apiKey && apiKey.trim().length >= 20) {
        // Here you would actually validate the key with a Gemini API call
        return { valid: true };
      }
      return { valid: false, error: 'Invalid Gemini API key format.' };
    } catch (error: any) {
      console.error('Gemini API key test failed:', error);
      let errorMessage = 'Unknown error validating Gemini API key';

      if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }

      return { valid: false, error: errorMessage };
    }
  }

  /**
   * Test Anthropic API key
   * Note: This is a simplified implementation since we don't have the actual Anthropic client
   */
  private async testAnthropicKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      // For now, we'll just do a basic check to ensure the key exists and has valid format
      // In production, you would connect to the Anthropic API and validate the key
      if (apiKey && /^sk-ant-[a-zA-Z0-9]{32,}$/.test(apiKey.trim())) {
        // Here you would actually validate the key with an Anthropic API call
        return { valid: true };
      }
      return { valid: false, error: 'Invalid Anthropic API key format.' };
    } catch (error: any) {
      console.error('Anthropic API key test failed:', error);
      let errorMessage = 'Unknown error validating Anthropic API key';

      if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }

      return { valid: false, error: errorMessage };
    }
  }

  /**
   * Test OpenRouter API key
   */
  private async testOpenRouterKey(apiKey: string): Promise<{valid: boolean, error?: string}> {
    try {
      // Basic format validation for OpenRouter API keys
      if (apiKey && /^sk-or-[a-zA-Z0-9]{32,}$/.test(apiKey.trim())) {
        // In a production environment, you would make an actual API call to validate
        // For now, we'll just check the format
        return { valid: true };
      }
      return { valid: false, error: 'Invalid OpenRouter API key format. Keys should start with sk-or-' };
    } catch (error: any) {
      console.error('OpenRouter API key test failed:', error);
      let errorMessage = 'Unknown error validating OpenRouter API key';

      if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }

      return { valid: false, error: errorMessage };
    }
  }
}

// Export a singleton instance
export const configHelper = new ConfigHelper();
