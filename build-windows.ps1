# Interview Coder - Windows Build Script (PowerShell)
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "Interview Coder - Windows Build Script" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

Write-Host "[1/5] Checking Node.js and npm..." -ForegroundColor Yellow
if (-not (Test-Command "node")) {
    Write-Host "ERROR: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

if (-not (Test-Command "npm")) {
    Write-Host "ERROR: npm is not available" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✓ Node.js and npm are available" -ForegroundColor Green
Write-Host ""

Write-Host "[2/5] Installing dependencies..." -ForegroundColor Yellow
try {
    npm install
    if ($LASTEXITCODE -ne 0) { throw "npm install failed" }
    Write-Host "✓ Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Failed to install dependencies" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

Write-Host "[3/5] Cleaning previous builds..." -ForegroundColor Yellow
try {
    npm run clean
    Write-Host "✓ Previous builds cleaned" -ForegroundColor Green
} catch {
    Write-Host "WARNING: Clean command failed, continuing..." -ForegroundColor Yellow
}
Write-Host ""

Write-Host "[4/5] Building the application..." -ForegroundColor Yellow
try {
    npm run build
    if ($LASTEXITCODE -ne 0) { throw "build failed" }
    Write-Host "✓ Application built successfully" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Build failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}
Write-Host ""

Write-Host "[5/5] Packaging for Windows..." -ForegroundColor Yellow
try {
    npx electron-builder build --win
    if ($LASTEXITCODE -ne 0) { throw "packaging failed" }
} catch {
    Write-Host "ERROR: Packaging failed" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "✓ BUILD COMPLETED SUCCESSFULLY!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Output files are located in the 'release' directory:" -ForegroundColor Cyan
Write-Host "- Installer: Interview Coder-Windows-[version]-x64.exe" -ForegroundColor White
Write-Host "- Portable: Interview Coder-Windows-[version]-x64.exe (portable)" -ForegroundColor White
Write-Host ""
Write-Host "Features included in the build:" -ForegroundColor Cyan
Write-Host "✓ Alt+Enter for MCQ mode (fast Qwen processing)" -ForegroundColor Green
Write-Host "✓ Ctrl+R for coding questions (QwQ reasoning)" -ForegroundColor Green
Write-Host "✓ Gemini text extraction for all scenarios" -ForegroundColor Green
Write-Host "✓ All keyboard shortcuts and functionality" -ForegroundColor Green
Write-Host ""
Read-Host "Press Enter to exit"
